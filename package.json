{"name": "emilocker-backend", "version": "1.0.0", "description": "EMILocker Backend API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed": "node scripts/seedData.js"}, "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "socket.io": "^4.7.4", "moment": "^2.29.4", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["api", "backend", "emilo<PERSON>"], "author": "EMILocker Team", "license": "MIT"}